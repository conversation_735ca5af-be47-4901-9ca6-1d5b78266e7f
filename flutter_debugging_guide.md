# Flutter 调试和热重载最佳实践

## 🔧 解决 "Could not find summary for library" 错误

### 快速解决方案
```bash
# 1. 完全清理
flutter clean

# 2. 清理全局缓存（可选）
# Windows:
Remove-Item -Recurse -Force "$env:LOCALAPPDATA\Pub\Cache"

# 3. 重新获取依赖
flutter pub get

# 4. 重新运行
flutter run
```

### 常见原因
1. **语法错误** - 检查最近修改的文件
2. **导入错误** - 确保所有导入的文件存在
3. **缓存问题** - 使用上述清理命令
4. **循环依赖** - 检查文件间的导入关系

## 🚀 热重载使用指南

### 热重载 (Hot Reload) - Ctrl+S 或 r
- ✅ 修改 UI 布局
- ✅ 改变样式和颜色
- ✅ 修改函数逻辑
- ✅ 调整 widget 属性

### 热重启 (Hot Restart) - Ctrl+Shift+F5 或 R
- ✅ 添加新的导入
- ✅ 修改 main() 函数
- ✅ 改变全局/静态变量
- ✅ 添加新的类

### 完全重启 - 停止并重新运行
- ✅ 修改 pubspec.yaml
- ✅ 添加新资源文件
- ✅ 修改原生代码
- ✅ 出现缓存错误时

## 🛡️ 预防措施

1. **定期清理缓存**
   ```bash
   flutter clean && flutter pub get
   ```

2. **使用静态分析**
   ```bash
   flutter analyze
   ```

3. **检查依赖冲突**
   ```bash
   flutter pub deps
   ```

4. **遵循编码规范**
   - 避免循环导入
   - 正确使用 BuildContext
   - 及时修复警告

## 📝 VS Code 快捷键
- `Ctrl+Shift+P` → "Flutter: Hot Reload"
- `Ctrl+Shift+P` → "Flutter: Hot Restart" 
- `Ctrl+Shift+P` → "Flutter: Clean"
