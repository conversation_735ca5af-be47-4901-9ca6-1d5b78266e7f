name: thoughtecho
description: 一款帮助你记录和分析思想的应用。
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.7.2 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.6
  provider: ^6.1.2
  sqflite: ^2.3.2
  sqflite_common_ffi: ^2.3.2
  sqlite3_flutter_libs: ^0.5.18
  path: ^1.8.3
  path_provider: ^2.1.2
  dio: ^5.4.0
  uuid: ^4.3.3
  shared_preferences: ^2.5.2
  flutter_markdown: ^0.7.1
  intl: ^0.19.0
  url_launcher: ^6.2.4
  file_selector: ^1.0.2 # 使用Flutter官方维护的文件选择器插件
  share_plus: ^7.2.1 # 用于分享文件
  flex_color_scheme: ^7.3.1
  geolocator: ^11.1.0
  permission_handler: ^11.3.0
  flutter_secure_storage: ^9.0.0
  geocoding: ^2.1.1 # 使用系统提供的地理编码功能
  geocode: ^1.0.3 # 轻量级地理编码包，可配合系统功能使用
  mmkv: ^2.2.1 # 恢复到2.x版本，我们有回退机制处理32位设备
  ffi: ^2.1.0 # 显式添加FFI依赖，确保与最新的Dart兼容
  sqflite_common_ffi_web: ^0.3.0
  flex_color_picker: ^3.3.0
  lottie: ^2.7.0
  flutter_svg: ^2.0.7
  flutter_spinkit: ^5.2.0
  dynamic_color: ^1.6.6
  flutter_quill: ^11.4.0
  package_info_plus: ^8.3.0 # 更新版本解决依赖冲突
  logging: ^1.2.0 # 添加 logging 包用于日志管理
  logging_flutter: ^3.0.0 # 添加 logging_flutter 包用于Flutter日志管理

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.13.1

# 应用图标配置
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "icon.png"
  
# 字体配置
flutter:
  # 确保应用可以使用Material Design图标
  uses-material-design: true
  assets:
    - assets/lottie/
    - assets/icon.png